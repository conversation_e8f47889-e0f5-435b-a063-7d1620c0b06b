[debug] [2025-06-07T02:27:53.191Z] ----------------------------------------------------------------------
[debug] [2025-06-07T02:27:53.196Z] Command:       /Users/<USER>/.nvm/versions/node/v23.9.0/bin/node /Users/<USER>/.nvm/versions/node/v23.9.0/bin/firebase deploy --only functions
[debug] [2025-06-07T02:27:53.196Z] CLI Version:   14.5.1
[debug] [2025-06-07T02:27:53.196Z] Platform:      darwin
[debug] [2025-06-07T02:27:53.196Z] Node Version:  v23.9.0
[debug] [2025-06-07T02:27:53.196Z] Time:          Fri Jun 06 2025 21:27:53 GMT-0500 (Central Daylight Time)
[debug] [2025-06-07T02:27:53.196Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-07T02:27:53.289Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-07T02:27:53.290Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-07T02:27:53.290Z] [iam] checking project chefpal-a9abe for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-06-07T02:27:53.290Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:53.291Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:53.291Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe:testIamPermissions [none]
[debug] [2025-06-07T02:27:53.291Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe:testIamPermissions x-goog-quota-user=projects/chefpal-a9abe
[debug] [2025-06-07T02:27:53.291Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-06-07T02:27:53.661Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe:testIamPermissions 200
[debug] [2025-06-07T02:27:53.662Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-06-07T02:27:53.662Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:53.662Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:53.662Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/chefpal-a9abe/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-06-07T02:27:53.662Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/chefpal-a9abe/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-06-07T02:27:53.964Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/chefpal-a9abe/serviceAccounts/<EMAIL>:testIamPermissions 200
[debug] [2025-06-07T02:27:53.964Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/chefpal-a9abe/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[info] 
[info] === Deploying to 'chefpal-a9abe'...
[info] 
[info] i  deploying functions 
[debug] [2025-06-07T02:27:53.965Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:53.965Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:53.965Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/cloudresourcemanager.googleapis.com [none]
[debug] [2025-06-07T02:27:53.965Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/cloudresourcemanager.googleapis.com x-goog-quota-user=projects/chefpal-a9abe
[debug] [2025-06-07T02:27:54.326Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/cloudresourcemanager.googleapis.com 200
[debug] [2025-06-07T02:27:54.326Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/cloudresourcemanager.googleapis.com [omitted]
[debug] [2025-06-07T02:27:54.326Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:54.326Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:54.326Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe [none]
[debug] [2025-06-07T02:27:54.427Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe 200
[debug] [2025-06-07T02:27:54.427Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe {"projectNumber":"116153101016","projectId":"chefpal-a9abe","lifecycleState":"ACTIVE","name":"chefpal","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-03-21T02:56:02.438112Z"}
[info] i  functions: preparing codebase default for deployment 
[info] i  functions: ensuring required API cloudfunctions.googleapis.com is enabled... 
[debug] [2025-06-07T02:27:54.428Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:54.428Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:54.428Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:54.428Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API cloudbuild.googleapis.com is enabled... 
[debug] [2025-06-07T02:27:54.428Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:54.428Z] Checked if tokens are valid: true, expires at: *************
[info] i  artifactregistry: ensuring required API artifactregistry.googleapis.com is enabled... 
[debug] [2025-06-07T02:27:54.428Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:54.428Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-07T02:27:54.428Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/cloudfunctions.googleapis.com [none]
[debug] [2025-06-07T02:27:54.428Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/cloudfunctions.googleapis.com x-goog-quota-user=projects/chefpal-a9abe
[debug] [2025-06-07T02:27:54.429Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/runtimeconfig.googleapis.com [none]
[debug] [2025-06-07T02:27:54.429Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/runtimeconfig.googleapis.com x-goog-quota-user=projects/chefpal-a9abe
[debug] [2025-06-07T02:27:54.430Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/cloudbuild.googleapis.com [none]
[debug] [2025-06-07T02:27:54.430Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/cloudbuild.googleapis.com x-goog-quota-user=projects/chefpal-a9abe
[debug] [2025-06-07T02:27:54.431Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/artifactregistry.googleapis.com [none]
[debug] [2025-06-07T02:27:54.431Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/artifactregistry.googleapis.com x-goog-quota-user=projects/chefpal-a9abe
